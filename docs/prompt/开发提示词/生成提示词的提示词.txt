豆包提示词:从图片内容转换为markdown文档，其中的图表以表格数据输出，如果图表数据无法准确解析请生成模拟数据（不要用“...”或文字描述替代），并将图表样式备注在数据表格后，要求内容完整无遗漏


(中文!中文!中文!答复)生成新的提示词文件,达到与[docs/prompt/7_价值评测/proms/prom_价值测评_s3_md2json.md]相同的效果:
1,markdown_content部分:参考文件[docs/prompt/6_行业政策分析/source/详细报告.md]里的内容作为
2,json_structure_definition部分:可以照搬(除非现有JSON结构无法满足界面要求,则需要你给出优化建议)
3,json_template部分:参考界面效果[docs/prompt/6_行业政策分析/source/界面内容提取.md],给出对应的JSON模板
4,system_prompt和user_prompt部分:参考[prom_价值测评_s3_md2json.md]相关部分,完成markdown_content+json_template=>json_report的转换
注意事项:
1,最终生成的提示词文件,要求其能产生图文并茂的JSON输出,因此需要充分挖掘数据内涵,尽量利用CHART控件呈现数据趋势对比等信息,且选择差异化图表类型以呈现界面多样性;
2,生成的文本内容具有一定的摘要性,因此需要限定输出文字数量:单独输出的文字段落,字数不能超过60字;列表项单项文字内容不能超过30字;
3,单位信息的呈现:单个图表中若数据单位相同则呈现在图表标题上,不要在每个数据分类中重复呈现单位信息;当图表中同一组数据(含标题),大部分超过或接近10000时,转换单位为万(显示单位并保留两位小数)



(中文!中文!中文!答复)基于提示词[prom_月度报告_s2_md2json.md]生成了结果[output_月度报告_s2.json],请修改提示词以便将数据最大程度使用图表呈现:
1,仅必要情况下,可以将单张表格数据拆分成多个图表呈现;
2,已经转换成图表的部分,也请推断是否恰当,可以进行重新定义修改;

(中文!中文!中文!答复)基于提示词[prom_月度报告_s2_md2json.md]生成了结果[output_月度报告_s2.json],请对提示词做如下修改,使生成更合理的输出:
1,观察["serial": "2.1.1"与"serial": "2.1.2"]["serial": "2.2.1"与"serial": "2.2.2"](或许还有其他),可以合并为一张MIXED样式的图表,成交套数用柱状,环比变化使用折线;
2,综合分析图表的拆分情况,强调相关数据尽量合并呈现,仅在数据相关性不大的情况下才拆分呈现;

请继续


观察[prom_房产生活专家_s3_md2json.md]生成的结果[output_房产生活专家.json],发现两处问题:
1,段落性文字内容普遍偏长,需要将之前定义的100字数限制缩减为60字以内;
2,图表["serial": "2.1.3"]与[ "serial": "2.2.2"]从标题内容上看不出区别,请根据数据内容给出差异化呈现;

查看由[prom_政策解读_md2json.md]生成的输出[output_政策解读_s2_o1.json]中,请修改提示词使图表["serial": "3.1.1"]的标题从元改为万元(例如"20000元/㎡"改为"2万元/㎡")


(中文!中文!中文!答复)请参考文档[docs/prompt/Markdown转换JSON/v4_progressive/step1_content_analysis.md]的分段结构,将提示词[docs/prompt/Markdown转换JSON/v5_基于JSON模板生成/ai_conversion_prompt.md]调整为[system_prompt\user_prompt\...]等分段格式